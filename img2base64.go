package main

import (
	"encoding/base64"
	"fmt"
	"io"
	"net/http"
	"strings"
)

func main() {
	// Placeholder for base64 image string
	base64Image := "<insert exampleImage.base64 here before building>"

	// Handle image conversion
	http.HandleFunc("/convert", func(w http.ResponseWriter, r *http.Request) {
		if r.Method != http.MethodPost {
			http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
			return
		}

		// Handle file upload
		if file, header, err := r.FormFile("file"); err == nil {
			defer file.Close()

			// Check if the file is an image
			contentType := header.Header.Get("Content-Type")
			if !strings.HasPrefix(contentType, "image/") {
				http.Error(w, "Uploaded file is not an image", http.StatusBadRequest)
				return
			}

			// Read the file data
			imageData, err := io.ReadAll(file)
			if err != nil {
				http.Error(w, fmt.Sprintf("Error reading file: %v", err), http.StatusInternalServerError)
				return
			}

			// Convert to base64
			base64Str := base64.StdEncoding.EncodeToString(imageData)
			base64Data := fmt.Sprintf("data:%s;base64,%s", contentType, base64Str)

			// Return the base64 data
			w.Header().Set("Content-Type", "text/plain")
			fmt.Fprint(w, base64Data)
			return
		}

		// Handle URL conversion
		imageURL := r.FormValue("url")
		if imageURL == "" {
			http.Error(w, "URL parameter is required", http.StatusBadRequest)
			return
		}

		// Fetch the image
		resp, err := http.Get(imageURL)
		if err != nil {
			http.Error(w, fmt.Sprintf("Error fetching image: %v", err), http.StatusInternalServerError)
			return
		}
		defer resp.Body.Close()

		// Check if the response is an image
		contentType := resp.Header.Get("Content-Type")
		if !strings.HasPrefix(contentType, "image/") {
			http.Error(w, "URL does not point to an image", http.StatusBadRequest)
			return
		}

		// Read the image data
		imageData, err := io.ReadAll(resp.Body)
		if err != nil {
			http.Error(w, fmt.Sprintf("Error reading image data: %v", err), http.StatusInternalServerError)
			return
		}

		// Convert to base64
		base64Str := base64.StdEncoding.EncodeToString(imageData)
		base64Data := fmt.Sprintf("data:%s;base64,%s", contentType, base64Str)

		// Return the base64 data
		w.Header().Set("Content-Type", "text/plain")
		fmt.Fprint(w, base64Data)
	})

	http.HandleFunc("/", func(w http.ResponseWriter, r *http.Request) {
		// Set security headers
		w.Header().Set("Content-Security-Policy", "default-src 'self'; img-src 'self' data:; style-src 'self' 'unsafe-inline'; script-src 'self' 'unsafe-inline'")
		w.Header().Set("X-Content-Type-Options", "nosniff")
		w.Header().Set("X-Frame-Options", "DENY")

		html := fmt.Sprintf(`
			<!DOCTYPE html>
			<html lang="en">
			<head>
				<meta charset="UTF-8">
				<meta http-equiv="Content-Security-Policy" content="default-src 'self'; img-src 'self' data:; style-src 'self' 'unsafe-inline'; script-src 'self' 'unsafe-inline'">
				<title>Image URL to Base64 Converter</title>
				<style>
					body {
						background-color: #111;
						color: white;
						font-family: sans-serif;
						margin: 0;
						padding: 20px;
					}
					input[type="text"], textarea {
						width: 100%%;
						box-sizing: border-box;
						margin-bottom: 10px;
						padding: 10px;
						border-radius: 4px;
						border: none;
						font-size: 14px;
					}
					button {
						background-color: #28a745;
						color: white;
						padding: 10px 20px;
						border: none;
						border-radius: 4px;
						cursor: pointer;
						font-size: 14px;
						margin-bottom: 10px;
						display: inline-block;
					}
					.instructions-wrapper {
						display: flex;
						justify-content: space-between;
						align-items: flex-start;
						gap: 20px;
						flex-wrap: wrap;
					}
					.instructions-list {
						background: #000;
						padding: 10px;
						border-left: 4px solid #00f;
						flex: 1 1 300px;
					}
					.instructions-image {
						flex: 0 0 300px;
						text-align: center;
					}
					.instructions-image img {
						max-width: 100%%;
						height: auto;
						border: 1px solid #444;
					}
					#status {
						margin: 10px 0;
						padding: 10px;
						border-radius: 4px;
						display: none;
					}
					.error {
						background-color: #dc3545;
						color: white;
					}
					.success {
						background-color: #28a745;
						color: white;
					}
					.tab {
						overflow: hidden;
						border: 1px solid #444;
						background-color: #222;
						border-radius: 4px 4px 0 0;
					}
					.tab button {
						background-color: inherit;
						float: left;
						border: none;
						outline: none;
						cursor: pointer;
						padding: 14px 16px;
						transition: 0.3s;
						margin: 0;
					}
					.tab button:hover {
						background-color: #333;
					}
					.tab button.active {
						background-color: #28a745;
					}
					.tabcontent {
						display: none;
						padding: 20px;
						border: 1px solid #444;
						border-top: none;
						border-radius: 0 0 4px 4px;
					}
					.file-input-wrapper {
						position: relative;
						margin-bottom: 10px;
					}
					.file-input-wrapper input[type="file"] {
						width: 100%%;
						padding: 10px;
						background: #222;
						border: 1px solid #444;
						border-radius: 4px;
						color: white;
					}
				</style>
			</head>
			<body>
				<h2>Image URL to Base64 Converter : KnowBe4 Image Embed</h2>
				<div style="background:#222;padding:10px;border-left:4px solid #0f0;margin-bottom:10px">
					<strong>KnowBe4 emails use <span style="color:#0cf">URL links</span></strong> which with modern email programs get blocked from loading for security reasons. As such, KnowBe4 Templates need to have their email images converted to base64. This tool formats the image for pasting directly into the URL field.
				</div>

				<div class="tab">
					<button class="tablinks active" onclick="openTab(event, 'urlTab')">URL Input</button>
					<button class="tablinks" onclick="openTab(event, 'fileTab')">File Upload</button>
				</div>

				<div id="urlTab" class="tabcontent" style="display: block;">
					<input type="text" id="imageUrl" placeholder="Enter image URL..." />
					<br>
					<button onclick="encodeImage()">Encode</button>
				</div>

				<div id="fileTab" class="tabcontent">
					<div class="file-input-wrapper">
						<input type="file" id="imageFile" accept="image/*" />
					</div>
					<button onclick="encodeFile()">Encode</button>
				</div>

				<div id="status"></div>
				<textarea id="base64Output" rows="10"></textarea>
				<br>
				<button onclick="copyToClipboard()">Copy to Clipboard</button>
				<br>
                 <hr>
                <br>
                  <h2>KnowBe4 Update Instructions</h2>
				<div class="instructions-wrapper">
					<div class="instructions-list">
						<ul>
							<li>Inside KnowBe4 Template, double click the image you need to convert.</li>
							<li>Copy the URL provided</li>
							<li>Paste in the URL field above</li>
							<li>Click Encode</li>
							<li>Click Copy to Clipboard</li>
							<li>Note the dimension currently set for the image in the image settings below the KnowBe4 Image URL</li>
							<li>Paste the content directly into the URL field for the image in KnowBe4</li>
							<li>Restore the image settings back to what they were</li>
							<li><em>(If you lost the settings, click cancel and do the process again)</em></li>
						</ul>
					</div>
					<div class="instructions-image">
						<img src="data:image/png;base64,%s" alt="Example image" />
					</div>
				</div>

				<script>
					function showStatus(message, isError) {
						const status = document.getElementById('status');
						status.textContent = message;
						status.style.display = 'block';
						status.className = isError ? 'error' : 'success';
					}

					function openTab(evt, tabName) {
						var i, tabcontent, tablinks;
						tabcontent = document.getElementsByClassName("tabcontent");
						for (i = 0; i < tabcontent.length; i++) {
							tabcontent[i].style.display = "none";
						}
						tablinks = document.getElementsByClassName("tablinks");
						for (i = 0; i < tablinks.length; i++) {
							tablinks[i].className = tablinks[i].className.replace(" active", "");
						}
						document.getElementById(tabName).style.display = "block";
						evt.currentTarget.className += " active";
					}

					async function encodeImage() {
						const imageUrl = document.getElementById('imageUrl').value.trim();
						if (!imageUrl) {
							showStatus('Please enter an image URL', true);
							return;
						}

						try {
							const formData = new FormData();
							formData.append('url', imageUrl);

							const response = await fetch('/convert', {
								method: 'POST',
								body: formData
							});

							if (!response.ok) {
								const error = await response.text();
								throw new Error(error);
							}

							const base64data = await response.text();
							document.getElementById('base64Output').value = base64data;
							showStatus('Image successfully converted to base64', false);
						} catch (error) {
							showStatus('Error: ' + error.message, true);
						}
					}

					async function encodeFile() {
						const fileInput = document.getElementById('imageFile');
						if (!fileInput.files || !fileInput.files[0]) {
							showStatus('Please select an image file', true);
							return;
						}

						try {
							const formData = new FormData();
							formData.append('file', fileInput.files[0]);

							const response = await fetch('/convert', {
								method: 'POST',
								body: formData
							});

							if (!response.ok) {
								const error = await response.text();
								throw new Error(error);
							}

							const base64data = await response.text();
							document.getElementById('base64Output').value = base64data;
							showStatus('Image successfully converted to base64', false);
						} catch (error) {
							showStatus('Error: ' + error.message, true);
						}
					}

					function copyToClipboard() {
						const textarea = document.getElementById('base64Output');
						if (!textarea.value) {
							showStatus('No base64 data to copy', true);
							return;
						}
						textarea.select();
						document.execCommand('copy');
						showStatus('Copied to clipboard!', false);
					}

					// Prevent any automatic image conversion
					document.addEventListener('DOMContentLoaded', function() {
						// Clear any existing base64 data
						document.getElementById('base64Output').value = '';
						
						// Prevent default behavior for image URLs
						document.addEventListener('click', function(e) {
							if (e.target.tagName === 'A' && e.target.href.match(/\.(jpg|jpeg|png|gif|webp)$/i)) {
								e.preventDefault();
							}
						}, true);
					});
				</script>
			</body>
			</html>`, base64Image)

		w.Header().Set("Content-Type", "text/html")
		fmt.Fprint(w, html)
	})

	fmt.Println("Server running at http://localhost:8080")
	http.ListenAndServe(":8080", nil)
}

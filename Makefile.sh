#!/bin/bash

# Store the original placeholder text
PLACEHOLDER="<insert exampleImage.base64 here before building>"

# Function to revert the placeholder text
revert_placeholder() {
    if [ -n "$BASE64_CONTENT" ]; then
        # Escape special characters in the base64 content
        ESCAPED_CONTENT=$(echo "$BASE64_CONTENT" | sed 's/[\/&]/\\&/g')
        sed -i "s|$ESCAPED_CONTENT|$PLACEHOLDER|g" img2base64.go
        echo "Reverted placeholder text in img2base64.go"
    fi
}

# Set up trap to ensure cleanup happens
trap revert_placeholder EXIT

# Check if exampleImage.base64 exists
if [ ! -f "exampleImage.base64" ]; then
    echo "Error: exampleImage.base64 file not found!"
    exit 1
fi

# Find Go executable
GO_CMD="/usr/local/go/bin/go"
if [ ! -f "$GO_CMD" ]; then
    GO_CMD="/usr/bin/go"
    if [ ! -f "$GO_CMD" ]; then
        echo "Error: Go executable not found in common locations"
        exit 1
    fi
fi

# Read the base64 content
BASE64_CONTENT=$(cat exampleImage.base64)

# Check if we got any content
if [ -z "$BASE64_CONTENT" ]; then
    echo "Error: exampleImage.base64 is empty!"
    exit 1
fi

# Escape special characters in the base64 content
ESCAPED_CONTENT=$(echo "$BASE64_CONTENT" | sed 's/[\/&]/\\&/g')

# Replace the placeholder in img2base64.go
sed -i "s|$PLACEHOLDER|$ESCAPED_CONTENT|g" img2base64.go

# Build the application
CGO_ENABLED=0 GOOS=linux GOARCH=amd64 $GO_CMD build -o img2base64 img2base64.go

# Check if build was successful
if [ $? -eq 0 ]; then
    echo "Build successful!"
    
    # Copy the binary to the remote server
    echo "Copying binary to remote server..."
    scp img2base64 labmaster@10.2.1.179:/tmp/
    
    if [ $? -eq 0 ]; then
        echo "Successfully copied binary to remote server"
    else
        echo "Failed to copy binary to remote server"
        exit 1
    fi
else
    echo "Build failed!"
    exit 1
fi
